:root {
    --primary-color: #4361ee;
    --secondary-color: #3f37c9;
    --accent-color: #4cc9f0;
    --success-color: rgb(102, 204, 51);
    --danger-color: #f72585;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --text-color: #2b2d42;
    --card-bg: #ffffff;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Press Start 2P', cursive;
    background-image: url("bhutan.jpg");
    background-repeat: no-repeat;
    background-size: cover;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 20px;
    color: var(--text-color);
    -webkit-font-smoothing: none;
}

/* Loading Screen Styles */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    color: white;
}

.loader {
    border: 5px solid #f3f3f3;
    border-top: 5px solid var(--accent-color);
    border-radius: 50%;
    width: 50px;
    height: 50px;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

.container {
    width: 100%;
    max-width: 800px;
    perspective: 1000px;
    opacity: 10;
    align-items: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.screen {
    background-color: var(--accent-color);
    border-radius: 16px;
    padding: 30px;
    box-shadow: var(--shadow);
    text-align: center;
    transition: transform 0.6s, opacity 0.6s;
    transform-style: preserve-3d;
    width: 900px;
}

#welcome-screen {
    opacity: 0.93;
}

#game-screen {
    background-color: var(--light-color);
}

#win-screen {
    opacity: 0.93;
}

.hidden {
    display: none;
    opacity: 0;
    transform: rotateY(180deg);
}

.welcome-content,
.win-content {
    animation: fadeIn 0.8s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

h1 {
    color: var(--light-color);
    margin-bottom: 20px;
    font-size: 2.5rem;
    font-weight: 700;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

h2 {
    color: rgb(0, 102, 204);
    margin: 20px 0 15px;
    font-size: 1.5rem;
}

h3 {
    color: rgb(0, 102, 204);
    margin: 20px 0 10px;
    font-size: 1.2rem;
}

.instructions {
    margin: 20px 0;
    text-align: center;
    background: var(--light-color);
    padding: 20px;
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.instructions p {
    margin: 15px 0 25px 25px;
    line-height: 1.6;
}

.difficulty-buttons {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-top: 25px;
}

.difficulty-btn {
    background-color: var(--accent-color);
    color: white;
    border: none;
    padding: 15px 25px;
    border-radius: 8px;
    font-size: 1rem;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    font-weight: bold;
}

.difficulty-btn:hover {
    background-color: rgb(0, 102, 204);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

#easy-btn:hover {
    color: var(--success-color);
}

#medium-btn:hover {
    color: yellow;
}

#hard-btn:hover {
    color: var(--danger-color);
}

.game-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    background: rgba(67, 97, 238, 0.1);
    padding: 15px;
    border-radius: 10px;
    flex-wrap: wrap;
    gap: 15px;
}

.level-info,
.stat {
    display: flex;
    align-items: center;
    gap: 15px;
}

.label {
    font-weight: 300;
    color: var(--secondary-color);
}

.value {
    font-weight: 600;
    color: var(--primary-color);
}

.stats {
    display: flex;
    gap: 20px;
}

.reset-btn {
    background-color: var(--success-color);
    color: white;
    padding: 10px 15px;
    border-radius: 8px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.reset-btn:hover {
    background-color: rgb(97, 195, 48);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.music-toggle {
    background-color: var(--accent-color);
    color: white;
    padding: 10px 15px;
    border-radius: 8px;
    font-size: 1.2rem;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 45px;
    height: 40px;
}

.music-toggle:hover {
    background-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.music-toggle.muted {
    background-color: var(--danger-color);
    opacity: 0.7;
}

.music-toggle.muted:hover {
    background-color: #e91e63;
}

.game-board {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 15px;
    margin: 25px 0;
}

.card {
    height: 150px;
    perspective: 1000px;
    cursor: pointer;
    transition: transform 0.2s;
}

.card:hover {
    transform: scale(1.03);
}

.card-inner {
    position: relative;
    width: 100%;
    height: 100%;
    transition: transform 0.6s;
    transform-style: preserve-3d;
    box-shadow: var(--shadow);
    border-radius: 10px;
}

.card.flipped .card-inner {
    transform: rotateY(180deg);
}

.card.matched {
    animation: pulse 0.5s;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.1);
    }

    100% {
        transform: scale(1);
    }
}

.card-front,
.card-back {
    position: absolute;
    width: 100%;
    height: 100%;
    backface-visibility: hidden;
    border-radius: 10px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 2rem;
    font-weight: bold;
    transition: var(--transition);
}

.card-front {
    background: linear-gradient(135deg, rgb(0, 102, 204) 0%, var(--accent-color) 100%);
    color: white;
}

.card-back {
    background: white;
    color: var(--text-color);
    transform: rotateY(180deg);
    box-shadow: inset 0 0 0 2px var(--primary-color);
}

.card-back img {
    width: 100%;
    height: 70%;
    object-fit: contain;
    background-size: cover;
    background-repeat: no-repeat;
    object-position: center;
}

button {
    border: none;
    padding: 12px 25px;
    border-radius: 8px;
    font-size: 1rem;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-weight: 500;
    font-family: 'Press Start 2P', cursive;
}

.back-btn {
    background-color: rgb(153, 102, 51);
    color: white;
    margin-top: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    vertical-align: middle;
}

.back-btn .icon {
    font-size: 28px;
    margin-right: 15px;
    margin-bottom: 4px;
}

.back-btn:hover {
    background-color: rgb(127, 78, 30);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.win-message {
    font-size: 1.2rem;
    margin: 15px 0;
    color: var(--secondary-color);
}

.final-stats {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin: 25px 0;
    background: rgba(67, 97, 238, 0.05);
    padding: 20px;
    border-radius: 10px;
    text-align: left;
}

.final-stat {
    display: flex;
    justify-content: space-between;
}

.play-again-btn {
    background-color: var(--success-color);
    color: white;
    margin-top: 20px;
    padding: 15px 30px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.play-again-btn:hover {
    background-color: #3ac05e;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Win text section */
.win-text-section {
    text-align: center;
    margin-bottom: 20px;
}

.win-text-section h1 {
    margin: 0 0 10px 0;
}

.win-text-section .win-message {
    margin: 0;
}

/* Celebration GIF/Reward Image */
.celebration-gif {
    width: 300px;
    height: 200px;
    margin: 0 auto 20px;
    border-radius: 20px;
    overflow: hidden;
    background-color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
}

.celebration-gif img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    border-radius: 20px;
}

/* Reward image specific styling for easy mode */
.celebration-gif.reward-mode {
    background-color: transparent;
    border: 3px solid var(--accent-color);
    box-shadow: 0 4px 15px rgba(67, 97, 238, 0.3);
}

.celebration-gif.reward-mode img {
    object-fit: cover;
    border-radius: 17px;
}

.win-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 25px;
    flex-wrap: wrap;
}

.win-buttons .back-btn {
    background-color: rgb(127, 78, 30);
    margin-top: 20px;
    padding: 15px 30px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.win-buttons .back-btn:hover {
    background-color: rgb(127, 78, 30);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Animations */
@keyframes cardFlip {
    0% {
        transform: rotateY(0);
    }

    50% {
        transform: rotateY(90deg);
    }

    100% {
        transform: rotateY(180deg);
    }
}

@keyframes celebration {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.2);
    }

    100% {
        transform: scale(1);
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .game-board {
        grid-template-columns: repeat(3, 1fr);
    }

    h1 {
        font-size: 2rem;
    }

    .game-header {
        flex-direction: column;
        align-items: flex-start;
    }
}

@media (max-width: 480px) {
    .game-board {
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
    }

    .card {
        height: 80px;
    }

    .card-front,
    .card-back {
        font-size: 1.5rem;
    }
}