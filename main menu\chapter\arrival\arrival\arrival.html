<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pixel Art Animation</title>
    <link rel="stylesheet" href="arrival.css">
    <style>
        body {
            margin: 0;
            overflow: hidden; /* Prevent scrolling */
            height: 100vh; /* Full viewport height */
            position: relative; /* Needed for absolute positioning of children */
        }
        
        #background {
            position: fixed;
            width: 100%;
            height: 100%;
            object-fit: cover; /* Ensure it covers the whole screen */
            z-index: -1; /* Put it behind other elements */
        }
        
        #game-container {
            position: relative;
            width: 100%;
            height: 100vh;
        }
        
        #girl, #treasure, #demon {
            position: absolute;
        }

        #continue-button {
            position: fixed;
            bottom: 50px;
            left: 50%;
            transform: translateX(-50%);
            padding: 15px 30px;
            background-color: #4a4a4a;
            color: white;
            border: 2px solid #fff;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 16px;
            cursor: pointer;
            display: none;
            z-index: 10;
        }

        #continue-button:hover {
            background-color: #666;
        }
    </style>
</head>
<body>
    <!-- Black screen overlay for fade-in transition -->
    <div id="black-screen-overlay" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: black; z-index: 10000; opacity: 1; transition: opacity 2s ease-out; pointer-events: none;"></div>

    <img id="background" src="Frame 17 (4).png" alt="Pixel Art Background">
    <div id="game-container">
        <img id="girl" src="women.png" alt="Girl Character">
        <img id="treasure" src="Group 97.png" alt="Treasure Orb">
        <img id="demon" src="sinmo side 1.png" alt="Demon Character">
    </div>

    <button id="continue-button">Continue to Dialogue</button>

    <script src="arrival.js"></script>
</body>
</html>