document.addEventListener('DOMContentLoaded', () => {
    const dialogueBox = document.getElementById('dialogue-box');
    const dialogueText = document.getElementById('dialogue-text');
    const nextButton = document.getElementById('next-button');

    // Dialogue lines
    const dialogueLines = [
        "Thank you, brave soul. I was lost in darkness, consumed by my own turmoil...",
        "I can feel the light returning to me, the warmth of hope...",
        "Let us move forward together. Thank you for your kindness..."
    ];
    let currentDialogueIndex = 0;

    // Show dialogue with first message
    function showDialogue() {
        dialogueBox.classList.add('visible');
        displayNextDialogueLine();
    }

    // Hide dialogue
    function hideDialogue() {
        dialogueBox.classList.remove('visible');
    }

    // Display next dialogue line
    function displayNextDialogueLine() {
        if (currentDialogueIndex < dialogueLines.length) {
            dialogueText.textContent = dialogueLines[currentDialogueIndex];
            currentDialogueIndex++;
            nextButton.textContent = currentDialogueIndex === dialogueLines.length ? "Close" : "Next";
        } else {
            hideDialogue();
            // Redirect to ending scene after dialogue completion
            setTimeout(() => {
                redirectToEndingScene();
            }, 1000); // Wait 1 second after dialogue closes
        }
    }

    // Event listener for next button
    nextButton.addEventListener('click', displayNextDialogueLine);

    // Show dialogue after short delay (1 second)
    setTimeout(showDialogue, 1000);
});

// Function to handle redirect to ending scene with black screen transition
function redirectToEndingScene() {
    console.log('Dialogue completed, redirecting to ending scene...');

    // Get the black screen overlay
    const blackScreen = document.getElementById('black-screen-overlay');
    if (blackScreen) {
        // Show and fade in the black screen
        blackScreen.style.display = 'block';
        setTimeout(() => {
            blackScreen.style.opacity = '1';
        }, 50);

        // Redirect after fade completes
        setTimeout(() => {
            window.location.href = '../../ending scene/index.html';
        }, 1800); // 1.8 seconds to allow fade to complete
    } else {
        // Fallback if no black screen overlay
        window.location.href = '../../ending scene/index.html';
    }
}