body {
    margin: 0;
    overflow: hidden;
    font-family: 'Arial', sans-serif;
    background-color: #000;
}

/* Black screen overlay for smooth transition */
#black-screen-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: black;
    z-index: 10000;
    opacity: 1;
    transition: opacity 2s ease-out;
    pointer-events: none;
}

#black-screen-overlay.fade-out {
    opacity: 0;
}

canvas {
    display: block;
    margin: 0 auto;
    background-color: #222;
}

#message {
    position: absolute;
    top: 20%;
    left: 50%;
    background-image: url('./Layer_1.png');
    height: 210px;
    width: 450px;
    transform: translate(-50%, -50%);
    font-family: 'Press Start 2P', cursive;
    /* background-color: rgba(0, 0, 0, 0.85); */
    color: black;
    /* padding: 25px; */
    /* border-radius: 15px; */
    /* border: 3px solid #daa520; */
    text-align: center;
    font-size: 15px;
    display: none;
    z-index: 90;
    /* box-shadow: 0 0 20px rgba(218, 165, 32, 0.5); */
    max-width: 80%;
}

#close-message {
    margin-top: 20px;
    top: 20%;
    padding: 10px 25px;
    background-color: lightgreen;
    color: #000;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-weight: bold;
    font-size: 18px;
    transition: all 0.3s;
}

#close-message:hover {
    background-color: #ffd700;
    transform: scale(1.05);
}