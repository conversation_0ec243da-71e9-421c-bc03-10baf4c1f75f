* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Press Start 2P', cursive;
    background-image: url('bg2.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background-attachment: fixed;
    min-height: 100vh;
    overflow-x: hidden;
    color: white;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}

/* Black screen overlay for smooth transition */
#black-screen-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: black;
    z-index: 10000;
    opacity: 1;
    transition: opacity 2s ease-out;
    pointer-events: none;
}

#black-screen-overlay.fade-out {
    opacity: 0;
}

.ending-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    padding: 20px;
    background: rgba(0, 0, 0, 0.3);
    animation: fadeIn 2s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.congratulations-section {
    text-align: center;
    margin-bottom: 40px;
    animation: slideDown 1.5s ease-out 0.5s both;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.congratulations-title {
    font-size: 2.5rem;
    color: #FFD700;
    margin-bottom: 20px;
    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9);
    animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
    from {
        text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9), 0 0 20px #FFD700;
    }
    to {
        text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9), 0 0 30px #FFD700, 0 0 40px #FFD700;
    }
}

.congratulations-text {
    font-size: 1rem;
    line-height: 1.6;
    max-width: 600px;
    margin: 0 auto;
}

.relic-section {
    text-align: center;
    margin-bottom: 40px;
    animation: scaleIn 1.5s ease-out 1s both;
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.5);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.relic-container {
    position: relative;
    display: inline-block;
    margin-bottom: 20px;
}

.relic-image {
    width: 200px;
    height: 200px;
    object-fit: contain;
    filter: drop-shadow(0 0 20px rgba(255, 215, 0, 0.8));
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

.relic-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 220px;
    height: 220px;
    background: radial-gradient(circle, rgba(255, 215, 0, 0.3) 0%, transparent 70%);
    border-radius: 50%;
    animation: pulse 2s ease-in-out infinite;
    pointer-events: none;
}

@keyframes pulse {
    0%, 100% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 0.7;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.1);
        opacity: 0.3;
    }
}

.relic-text {
    font-size: 1.2rem;
    color: #FFD700;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}

.final-message {
    text-align: center;
    animation: fadeInUp 1.5s ease-out 1.5s both;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.final-message p {
    font-size: 1rem;
    margin-bottom: 10px;
    line-height: 1.6;
}

/* Responsive design */
@media (max-width: 768px) {
    .congratulations-title {
        font-size: 1.8rem;
    }
    
    .congratulations-text,
    .final-message p {
        font-size: 0.8rem;
    }
    
    .relic-text {
        font-size: 1rem;
    }
    
    .relic-image {
        width: 150px;
        height: 150px;
    }
    
    .relic-glow {
        width: 170px;
        height: 170px;
    }
}

@media (max-width: 480px) {
    .congratulations-title {
        font-size: 1.4rem;
    }
    
    .congratulations-text,
    .final-message p {
        font-size: 0.7rem;
    }
    
    .relic-image {
        width: 120px;
        height: 120px;
    }
    
    .relic-glow {
        width: 140px;
        height: 140px;
    }
}
