// Game Canvas Setup
const canvas = document.getElementById("gameCanvas");
const ctx = canvas.getContext("2d");
const messageDiv = document.getElementById("message");
const congratsDiv = document.getElementById("congrats-message");
const closeButton = document.getElementById("close-message");

// Game Images - Start with newchorten images directly
const images = {
    background: loadImage("3ppl.png"), // Use new background directly
    newBackground: loadImage("3ppl.png"), // Keep same for consistency
    girlWalk: loadImage("girlwalk.png"),
    womanWalk: loadImage("female character1.png"), // Use woman character directly
    dorji: loadImage("dorji.png"),
    key: loadImage("key.png"),
    lamp: loadImage("lamp.png"),
    temple: loadImage("chorten.png")
};

// Game State - Girl stays in position
const woman = {  // Keep variable name for consistency
    x: 1000,  // Start at final position (no walking)
    y: 470,   // Match the Y position from chorten
    width: 80,
    height: 190,
    speed: 3,  // Walking speed (not used)
    targetX: 1000,  // Already at target position
    targetY: 470,   // Keep same Y position
    isMoving: false,  // No movement needed
    useGirlImage: true  // Use girl image for walking
};

// Scene transition state - Girl is already in position
const sceneTransition = {
    phase: 'complete', // Girl is already in final position
    backgroundOpacity: 1, // Show new background immediately
    characterTransition: 0, // Start with girl character
    templeOpacity: 1, // Show temple immediately
    transitionSpeed: 0.02
};

const temple = {
    x: 1050,
    y: 150,
    width: 200,
    height: 200
};

// Initialize Game
function init() {
    // Use same canvas dimensions as chorten for seamless transition
    canvas.width = images.background.width;
    canvas.height = images.background.height;

    // Fade out black screen slowly after page loads
    const blackScreen = document.getElementById('black-screen-overlay');
    if (blackScreen) {
        setTimeout(() => {
            blackScreen.classList.add('fade-out');
            // Remove the overlay after fade completes
            setTimeout(() => {
                blackScreen.remove();
            }, 2000);
        }, 500);
    }

    closeButton.addEventListener("click", () => {
        // Create black screen transition before redirect
        const blackScreen = document.createElement('div');
        blackScreen.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: black;
            z-index: 9999;
            opacity: 0;
            transition: opacity 1s ease-in-out;
        `;
        document.body.appendChild(blackScreen);

        // Fade to black
        setTimeout(() => {
            blackScreen.style.opacity = '1';
        }, 100);

        // Redirect after fade completes
        setTimeout(() => {
            window.location.href = 'ending.html';
        }, 1500);
    });

    // Start scene transition after a brief moment
    setTimeout(() => {
        sceneTransition.phase = 'complete';
        // Show message immediately since girl is already in position
        setTimeout(() => {
            messageDiv.style.display = "block";
            // Trigger fade-in after a brief moment to ensure display is set
            setTimeout(() => {
                messageDiv.classList.add("fade-in");
            }, 50);
        }, 1000); // Show message after black screen fade completes
    }, 100);

    gameLoop();
}

// Show Congratulations
function showCongratulations() {
    congratsDiv.classList.remove("hidden");
    
    // Glitter burst after 2 seconds
    setTimeout(() => {
        createGlitterBurst(temple);
    }, 2000);
}

// Create Glitter Burst Effect
function createGlitterBurst(source) {
    const centerX = source.x + source.width/2;
    const centerY = source.y + source.height/2;
    
    for (let i = 0; i < 100; i++) {
        setTimeout(() => {
            const particle = document.createElement("div");
            particle.className = "glitter-particle";
            
            // Random direction and distance
            const angle = Math.random() * Math.PI * 2;
            const distance = 50 + Math.random() * 100;
            const tx = Math.cos(angle) * distance;
            const ty = Math.sin(angle) * distance;
            
            particle.style.setProperty("--tx", `${tx}px`);
            particle.style.setProperty("--ty", `${ty}px`);
            particle.style.left = `${centerX}px`;
            particle.style.top = `${centerY}px`;
            
            // Random color variation
            const hue = 50 + Math.random() * 10;
            particle.style.backgroundColor = `hsl(${hue}, 100%, 50%)`;
            
            document.body.appendChild(particle);
            
            // Remove after animation
            setTimeout(() => {
                particle.remove();
            }, 2000);
        }, Math.random() * 200);
    }
}

// Main Game Loop - Updated for smooth scene transition
function gameLoop() {
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Update scene transition
    updateSceneTransition();

    // Draw backgrounds with transition
    drawBackgrounds();

    // Draw temple with fade-in
    if (sceneTransition.templeOpacity > 0) {
        ctx.save();
        ctx.globalAlpha = sceneTransition.templeOpacity;
        ctx.drawImage(images.temple, temple.x, temple.y);
        ctx.restore();
    }

    // Woman is already in position, no movement needed
    // Movement logic removed since woman starts at final position

    // Draw character with transition
    drawCharacter();

    requestAnimationFrame(gameLoop);
}

function updateSceneTransition() {
    switch (sceneTransition.phase) {
        case 'moving':
            // Character is moving, no visual changes yet
            break;

        case 'transforming':
            // Gradually transition backgrounds and character
            sceneTransition.backgroundOpacity = Math.min(1, sceneTransition.backgroundOpacity + sceneTransition.transitionSpeed);
            sceneTransition.characterTransition = Math.min(1, sceneTransition.characterTransition + sceneTransition.transitionSpeed);
            sceneTransition.templeOpacity = Math.min(1, sceneTransition.templeOpacity + sceneTransition.transitionSpeed);

            if (sceneTransition.backgroundOpacity >= 1) {
                sceneTransition.phase = 'complete';
            }
            break;
    }
}

function drawBackgrounds() {
    // Draw original background
    ctx.drawImage(images.background, 0, 0);

    // Draw new background with fade-in
    if (sceneTransition.backgroundOpacity > 0) {
        ctx.save();
        ctx.globalAlpha = sceneTransition.backgroundOpacity;
        ctx.drawImage(images.newBackground, 0, 0);
        ctx.restore();
    }
}

function drawCharacter() {
    // Choose which character image to use based on transition
    let characterImage;
    if (sceneTransition.characterTransition < 0.5) {
        characterImage = images.girlWalk;
    } else {
        characterImage = images.womanWalk;
    }

    ctx.drawImage(
        characterImage,
        woman.x,
        woman.y,
        woman.width,
        woman.height
    );
}

// Helper function to load images
function loadImage(src) {
    const img = new Image();
    img.src = src;
    return img;
}

// Start game when images load
let loadedImages = 0;
Object.values(images).forEach(img => {
    img.onload = () => {
        loadedImages++;
        if (loadedImages === Object.keys(images).length) {
            init();
        }
    };
});
