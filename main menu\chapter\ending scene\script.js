window.onload = () => {
  // Handle black screen fade-out on page load
  const blackScreen = document.getElementById('black-screen-overlay');
  if (blackScreen) {
    setTimeout(() => {
      blackScreen.style.opacity = '0';
      // Remove the overlay after fade completes
      setTimeout(() => {
        blackScreen.remove();
      }, 2000);
    }, 500);
  }

  const girlBack = document.querySelector('.girl-back');
  const girlSide = document.querySelector('.girl-side');
  const endMessage = document.querySelector('.end-message');

  // Animate the girl being pulled up to the moon
  girlBack.style.transform = 'translateY(-400px)';

  // Animate the side-view girl walking to the house
  girlSide.style.left = '70%';

  // After animations, fade in the end message
  setTimeout(() => {
    endMessage.classList.add('show');
  }, 4000); // Matches character animation duration
};

