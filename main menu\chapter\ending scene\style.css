* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body, html {
  height: 100%;
  width: 100%;
  overflow: hidden;
  font-family: 'Press Start 2P', monospace;
}

.scene {
  position: relative;
  width: 100%;
  height: 100vh;
  background-color: #1e1e3f;
  overflow: hidden;
}

.background {
  position: absolute;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: 0;
}

.moon {
  position: absolute;
  top: 5%;
  left: 50%;
  transform: translateX(-50%);
  width: 120px;
  z-index: 1;
}

.rope {
  position: absolute;
  top: 15%;
  left: 50%;
  transform: translateX(-50%);
  width: 10px;
  height: 250px;
  z-index: 1;
}

.girl-back {
  position: absolute;
  bottom: 20%;
  left: 48%;
  width: 80px;
  z-index: 2;
  transition: transform 4s ease-in-out;
}

.girl-side {
  position: absolute;
  bottom: 10%;
  left: 35%;
  width: 60px;
  z-index: 3;
  transition: left 4s linear;
}

.house {
  position: absolute;
  bottom: 5%;
  right: 0%;
  width: 600px;
  z-index: 2;
}

.end-message {
  position: absolute;
  top: 30%;
  left: 50%;
  transform: translateX(-50%);
  width: 80%;
  text-align: center;
  color: white;
  z-index: 5;
  opacity: 0;
  transition: opacity 3s ease-in-out;
}

.end-message.show {
  opacity: 1;
}

.end-message h1 {
  font-size: 45px;
  margin-bottom: 20px;
}

.end-message p {
  font-size: 20px;
  line-height: 1.6;
  margin-top: 10px;
}
